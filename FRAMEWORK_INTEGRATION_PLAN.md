# 🧩 Framework Integration Plan

This document outlines the strategy for integrating the MongoDB AI Agent Boilerplate with popular AI frameworks. The goal is to be "framework-agnostic" by providing a set of powerful, standalone primitives and then demonstrating how to wrap them for use in any given ecosystem.

Our core philosophy is **"Provide, Don't Prescribe."** We provide the industry's best data and agent-operating-system primitives on MongoDB; developers can then use these powerful building blocks within the framework of their choice.

---

## 1. Core Integration Primitives

The boilerplate will expose the following core components via the **Storage Adapter Layer** and dedicated service classes. These are the key integration points.

- **`IEmbeddingStore`:**
  - **Functionality:** `upsert(vectors)` and `query(vector, filters)`.
  - **Integration:** Wraps the powerful hybrid search pipeline.
- **`IMemoryStore`:**
  - **Functionality:** `get_history(agent_id, session_id)` and `add_message(agent_id, session_id, message)`.
  - **Integration:** Provides access to both long-term (`agent_memory`) and short-term (`agent_working_memory`) storage.
- **`IToolExecutor`:**
  - **Functionality:** `execute_tool(tool_name, params)`.
  - **Integration:** A wrapper around the `tool_executions` logic that handles logging, validation, and permissions.
- **`IWorkflowManager`:**
  - **Functionality:** `start_workflow(workflow_name, input)` and `get_workflow_status(workflow_id)`.
  - **Integration:** The entry point for kicking off complex, multi-agent processes defined in the `agent_workflows` collection.

---

## 2. LangChain Integration Strategy

**Goal:** Make the boilerplate's components feel like native LangChain objects.

- **`VectorStore`:**
  - **Task:** Create a `MongoDBAgentVectorStore` class that inherits from `langchain.vectorstores.VectorStore`.
  - **Implementation:** The `add_documents` method will use our `IEmbeddingStore.upsert()`. The `similarity_search` method will call `IEmbeddingStore.query()`. This will allow developers to use our hybrid search capabilities seamlessly within LangChain.
- **`BaseChatMessageHistory`:**
  - **Task:** Create a `MongoDBAgentChatHistory` class that inherits from `langchain.memory.BaseChatMessageHistory`.
  - **Implementation:** The `messages` property and `add_message` method will be powered by our `IMemoryStore`, giving LangChain agents access to our sophisticated, multi-layered memory system.
- **`Tool`:**
  - **Task:** Create a function `make_langchain_tool_from_registry(tool_name)`.
  - **Implementation:** This function will read a tool's definition from the `agent_tools` collection (via the `resource_registry`) and dynamically create a LangChain `Tool` object, complete with name, description, and an `_arun` method that calls our `IToolExecutor`. This allows any tool registered in our system to be used by a LangChain agent.

---

## 3. CrewAI Integration Strategy

**Goal:** Empower CrewAI's collaborative agents with our robust backend for memory and task management.

- **`LongTermMemory` / `ShortTermMemory`:**
  - **Task:** Create a `MongoDBAgentMemoryHandler` for CrewAI.
  - **Implementation:** This handler will be used in a CrewAI agent's `memory` property. Its `save` and `load` methods will interact with our `IMemoryStore`, allowing different CrewAI agents to have distinct long-term memories while sharing context through the `agent_working_memory`.
- **`BaseTool`:**
  - **Task:** Create a `MongoDBAgentTool` class that inherits from CrewAI's `BaseTool`.
  - **Implementation:** Similar to the LangChain strategy, we will have a factory function that generates CrewAI-compatible tools from our `agent_tools` registry. This makes our entire tool ecosystem available to any CrewAI crew.
- **Task Management:**
  - **Task:** Demonstrate how a CrewAI `Task` can trigger a complex backend process.
  - **Implementation:** The `execute` method of a CrewAI task will call our `IWorkflowManager.start_workflow()`. The result of the task will be retrieved by polling `IWorkflowManager.get_workflow_status()`. This allows a simple CrewAI task to kick off a multi-agent workflow involving dozens of steps, all managed by our robust engine.

---

## 4. Generic Adapter Pattern (for other frameworks)

**Goal:** Provide a clear, simple pattern for integrating with any other framework (e.g., LlamaIndex, Autogen).

- **Strategy:** We will not create bespoke integrations for every framework. Instead, we will provide a well-documented "Generic Adapter" example.
- **Implementation:**
  1.  The example will show how to instantiate our core service classes (`EmbeddingStore`, `MemoryStore`, etc.).
  2.  It will then show how to write thin wrapper classes that conform to the target framework's interfaces (e.g., LlamaIndex's `BaseMemory` or `BaseVectorStore`).
  3.  The documentation will emphasize that because our primitives are so powerful, these adapters are often less than 50 lines of code.

This strategy ensures maximum reach and future-proofing without requiring us to maintain a dozen different integration packages. We provide the engine; the community can build the chassis.